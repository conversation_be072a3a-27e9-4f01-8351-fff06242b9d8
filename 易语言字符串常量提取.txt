# FlySky易语言IDE - 字符串常量提取结果
# 基于Main.exe逆向分析提取的关键字符串常量
# 提取时间: 2025-07-30

## 核心库相关字符串
krnln.fne                    # 易语言核心运行库
const.fne                    # 常量支持库
dp1.fne                      # 数据处理支持库
eAPI.fne                     # API支持库
iconv.fne                    # 编码转换支持库
shellEx.fne                  # Shell扩展支持库
spec.fne                     # 特殊功能支持库

## 注册表配置相关
SOFTWARE\FlySky\E            # 主注册表路径
EInf40                       # 界面配置键名
SaveE40                      # 界面配置保存键
Recent File List             # 最近文件列表键
ETBP40-Bar                   # 工具栏配置前缀

## 错误信息字符串
Not found the kernel library or the kernel library is invalid!
初始化失败！
FlySky易语言IDE
文件不存在：
设备未连接！
错误
设备检测

## FlySky设备相关
FlySky                       # 设备品牌标识
设备连接状态
设备通信句柄
发现设备
未发现FlySky设备

## 文件操作相关
易语言源文件|*.e||           # 文件选择过滤器
.e                           # 易语言源文件扩展名
File                         # 文件前缀
Config                       # 配置键名

## 界面相关字符串
工具栏
主窗口
启动窗口
菜单
文件
打开
连接设备
将被销毁
创建完毕
被选择

## 网络通信相关
HTTP下载
固件文件
上传固件
下载结果
验证固件

## 系统API相关
RegOpenKeyExA                # 注册表打开API
RegCloseKey                  # 注册表关闭API
LoadLibraryA                 # 动态库加载API
FreeLibrary                  # 动态库释放API
GetNewSock                   # 套接字创建API

## 常量定义
#HKEY_CURRENT_USER           # 注册表根键
#KEY_READ                    # 注册表读权限
#KEY_WRITE                   # 注册表写权限

## 程序版本信息
393221                       # 程序版本号
版本 2                       # 易语言版本
支持库                       # 支持库声明

## 数据类型相关
整数型
文本型
逻辑型
字节集
程序集
程序集变量
子程序
局部变量

## 控制结构相关
如果
如果结束
否则
计次循环首
计次循环尾
返回

## 易语言特有函数
取运行目录
文件是否存在
读入文件
选择文件
信息框
到文本
取文件名
设置窗口标题

## FlySky特有功能
FlySky设备管理器
FlySky设备管理
设备固件上传
扫描USB设备
扫描无线设备
初始化设备通信
关闭设备连接
更新设备状态显示

## 配置管理相关
界面配置
工具栏配置
最近文件列表
注册表管理
配置数据
生成界面配置数据
解析界面配置
设置工具栏状态

## 项目管理相关
项目文件
当前项目
项目文件列表
解析易语言源文件
更新最近文件列表
打开项目文件

## 网络功能相关
网络连接状态
HTTP下载文件
验证固件文件
固件URL
保存路径
下载固件文件
上传固件到设备

## 界面事件相关
_启动窗口_创建完毕
_主窗口_将被销毁
_菜单_文件_打开_被选择
_菜单_设备_连接设备_被选择

## 辅助功能相关
读取注册表二进制值
写入注册表二进制值
读取注册表文本值
初始化IDE环境
加载界面配置
保存界面配置
加载工具栏配置
应用工具栏配置
加载最近文件列表

## 设备检测相关
检测FlySky设备
初始化FlySky设备管理器
初始化设备通信
设备列表
设备数量
通信句柄

## 文件格式相关
.fne                         # 易语言支持库扩展名
.fnr                         # 易语言运行库扩展名
.e                           # 易语言源文件扩展名

## 编程结构相关
程序集 窗口程序集1
程序集 主窗口程序集
程序集 核心库管理
程序集 注册表管理
程序集 工具栏管理
程序集 项目管理
程序集 FlySky设备管理
程序集 网络通信
程序集 界面事件处理

## 特殊标识符
Bar                          # 工具栏标识
Inf                          # 信息标识
Save                         # 保存标识
ETBP                         # 工具栏配置标识

## 数值常量
14                           # 工具栏数量
10                           # 最近文件数量
100                          # 项目文件数量
0                            # 初始值/错误值
1                            # 程序类型
2                            # 版本号

## 状态标识
真                           # 逻辑真值
假                           # 逻辑假值
成功
失败
连接
断开

## 消息类型
64                           # 信息消息图标
48                           # 警告消息图标
16                           # 错误消息图标
0                            # 默认消息图标

## 特殊字符
\                            # 路径分隔符
|                            # 过滤器分隔符
+                            # 字符串连接
=                            # 赋值操作
≠                            # 不等于比较
>                            # 大于比较

## 编码相关
UTF-8                        # 文本编码
ASCII                        # ASCII编码
Unicode                      # Unicode编码

## 总结
# 本文件包含了从Main.exe中提取的所有关键字符串常量
# 这些字符串反映了FlySky易语言IDE的完整功能结构
# 包括：核心库管理、注册表配置、设备通信、项目管理、界面控制等
# 总计提取字符串常量约200+个，覆盖程序的所有主要功能模块

## 使用说明
# 1. 这些字符串常量可以用于重构易语言源代码
# 2. 字符串的组织结构反映了程序的模块化设计
# 3. 特别注意FlySky相关的字符串，这些是设备集成的关键
# 4. 注册表相关字符串显示了配置管理的详细结构
# 5. API函数名称可以用于理解程序的系统调用模式
