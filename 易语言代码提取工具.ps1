# 易语言代码提取工具
# 专门用于从Main.exe中提取易语言源代码
# 基于深度逆向分析结果开发

[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    Write-Host $Text -ForegroundColor $Color
}

function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Extract-ELanguageCode {
    param([string]$ExePath)
    
    Write-ColorText "开始提取易语言代码..." "Cyan"
    
    if (!(Test-Path $ExePath)) {
        Write-ColorText "错误：找不到文件 $ExePath" "Red"
        return $false
    }
    
    try {
        # 读取PE文件
        $fileBytes = [System.IO.File]::ReadAllBytes($ExePath)
        Write-ColorText "文件大小: $($fileBytes.Length) 字节" "Green"
        
        # 分析PE头
        $peOffset = [BitConverter]::ToInt32($fileBytes, 0x3C)
        Write-ColorText "PE头偏移: 0x$($peOffset.ToString('X'))" "Gray"
        
        # 查找.data段
        $dataSection = Find-DataSection $fileBytes $peOffset
        if ($dataSection) {
            Write-ColorText "找到.data段: 偏移=0x$($dataSection.Offset.ToString('X')), 大小=$($dataSection.Size)" "Green"
            
            # 提取易语言字节码
            Extract-ByteCode $fileBytes $dataSection
            
            # 提取字符串常量
            Extract-StringConstants $fileBytes $dataSection
            
            # 提取程序结构
            Extract-ProgramStructure $fileBytes $dataSection
            
            # 生成重构代码
            Generate-ReconstructedCode
            
            return $true
        }
        else {
            Write-ColorText "错误：未找到.data段" "Red"
            return $false
        }
    }
    catch {
        Write-ColorText "错误：$($_.Exception.Message)" "Red"
        return $false
    }
}

function Find-DataSection {
    param([byte[]]$FileBytes, [int]$PeOffset)
    
    # PE头 + 可选头大小
    $sectionTableOffset = $PeOffset + 24 + [BitConverter]::ToInt16($FileBytes, $PeOffset + 20)
    $numberOfSections = [BitConverter]::ToInt16($FileBytes, $PeOffset + 6)
    
    for ($i = 0; $i -lt $numberOfSections; $i++) {
        $sectionOffset = $sectionTableOffset + ($i * 40)
        $sectionName = [System.Text.Encoding]::ASCII.GetString($FileBytes, $sectionOffset, 8).TrimEnd([char]0)
        
        if ($sectionName -eq ".data") {
            $virtualSize = [BitConverter]::ToInt32($FileBytes, $sectionOffset + 8)
            $rawDataOffset = [BitConverter]::ToInt32($FileBytes, $sectionOffset + 20)
            $rawDataSize = [BitConverter]::ToInt32($FileBytes, $sectionOffset + 16)
            
            return @{
                Name = $sectionName
                VirtualSize = $virtualSize
                Offset = $rawDataOffset
                Size = $rawDataSize
            }
        }
    }
    
    return $null
}

function Extract-ByteCode {
    param([byte[]]$FileBytes, [hashtable]$DataSection)
    
    Write-ColorText "提取易语言字节码..." "Yellow"
    
    $startOffset = $DataSection.Offset
    $endOffset = $startOffset + $DataSection.Size
    
    # 查找字节码模式
    $bytecodePatterns = @()
    
    # 模式1: 32位整数序列 (可能的操作码)
    for ($i = $startOffset; $i -lt $endOffset - 8; $i += 4) {
        $value1 = [BitConverter]::ToInt32($FileBytes, $i)
        $value2 = [BitConverter]::ToInt32($FileBytes, $i + 4)
        
        # 查找小的正整数序列 (典型的操作码模式)
        if ($value1 -gt 0 -and $value1 -lt 1000 -and $value2 -gt 0 -and $value2 -lt 1000) {
            $bytecodePatterns += @{
                Offset = $i
                OpCode1 = $value1
                OpCode2 = $value2
            }
        }
    }
    
    Write-ColorText "发现 $($bytecodePatterns.Count) 个可能的字节码模式" "Green"
    
    # 保存字节码分析结果
    $bytecodeOutput = "# 易语言字节码分析结果`n`n"
    foreach ($pattern in $bytecodePatterns[0..50]) {  # 只显示前50个
        $bytecodeOutput += "偏移 0x$($pattern.Offset.ToString('X8')): OpCode=$($pattern.OpCode1), Param=$($pattern.OpCode2)`n"
    }
    
    [System.IO.File]::WriteAllText("易语言字节码分析.txt", $bytecodeOutput, [System.Text.Encoding]::UTF8)
    Write-ColorText "字节码分析结果已保存到: 易语言字节码分析.txt" "Green"
}

function Extract-StringConstants {
    param([byte[]]$FileBytes, [hashtable]$DataSection)
    
    Write-ColorText "提取字符串常量..." "Yellow"
    
    $startOffset = $DataSection.Offset
    $endOffset = $startOffset + $DataSection.Size
    
    $strings = @()
    $currentString = ""
    $stringStart = -1
    
    # 扫描可打印字符串
    for ($i = $startOffset; $i -lt $endOffset; $i++) {
        $byte = $FileBytes[$i]
        
        if ($byte -ge 32 -and $byte -le 126) {  # 可打印ASCII字符
            if ($stringStart -eq -1) {
                $stringStart = $i
            }
            $currentString += [char]$byte
        }
        else {
            if ($currentString.Length -gt 3) {  # 只保留长度大于3的字符串
                $strings += @{
                    Offset = $stringStart
                    Value = $currentString
                    Length = $currentString.Length
                }
            }
            $currentString = ""
            $stringStart = -1
        }
    }
    
    # 过滤出可能的易语言相关字符串
    $eLanguageStrings = $strings | Where-Object { 
        $_.Value -match "(FlySky|易语言|krnln|\.e$|\.fne$|注册表|窗口|程序集|子程序)" 
    }
    
    Write-ColorText "发现 $($strings.Count) 个字符串，其中 $($eLanguageStrings.Count) 个可能与易语言相关" "Green"
    
    # 保存字符串常量
    $stringOutput = "# 易语言字符串常量`n`n"
    foreach ($str in $eLanguageStrings) {
        $stringOutput += "偏移 0x$($str.Offset.ToString('X8')): `"$($str.Value)`"`n"
    }
    
    [System.IO.File]::WriteAllText("易语言字符串常量.txt", $stringOutput, [System.Text.Encoding]::UTF8)
    Write-ColorText "字符串常量已保存到: 易语言字符串常量.txt" "Green"
    
    return $eLanguageStrings
}

function Extract-ProgramStructure {
    param([byte[]]$FileBytes, [hashtable]$DataSection)
    
    Write-ColorText "分析程序结构..." "Yellow"
    
    # 基于逆向分析结果，查找程序结构信息
    $structures = @()
    
    # 查找可能的程序集信息
    $startOffset = $DataSection.Offset
    
    # 模式：查找连续的32位值，可能表示程序集结构
    for ($i = $startOffset; $i -lt $startOffset + $DataSection.Size - 16; $i += 4) {
        $val1 = [BitConverter]::ToInt32($FileBytes, $i)
        $val2 = [BitConverter]::ToInt32($FileBytes, $i + 4)
        $val3 = [BitConverter]::ToInt32($FileBytes, $i + 8)
        $val4 = [BitConverter]::ToInt32($FileBytes, $i + 12)
        
        # 查找特定的模式 (基于分析发现的模式)
        if ($val1 -eq 1 -and $val2 -eq 3 -and $val3 -eq 0 -and $val4 -eq 0) {
            $structures += @{
                Type = "ProgramHeader"
                Offset = $i
                Values = @($val1, $val2, $val3, $val4)
            }
        }
        
        # 查找版本信息模式
        if ($val1 -eq 393221) {  # 易语言版本号
            $structures += @{
                Type = "VersionInfo"
                Offset = $i
                Version = $val1
            }
        }
    }
    
    Write-ColorText "发现 $($structures.Count) 个程序结构" "Green"
    
    # 保存程序结构分析
    $structureOutput = "# 易语言程序结构分析`n`n"
    foreach ($struct in $structures) {
        $structureOutput += "类型: $($struct.Type), 偏移: 0x$($struct.Offset.ToString('X8'))`n"
        if ($struct.Values) {
            $structureOutput += "  值: $($struct.Values -join ', ')`n"
        }
        if ($struct.Version) {
            $structureOutput += "  版本: $($struct.Version)`n"
        }
        $structureOutput += "`n"
    }
    
    [System.IO.File]::WriteAllText("易语言程序结构.txt", $structureOutput, [System.Text.Encoding]::UTF8)
    Write-ColorText "程序结构分析已保存到: 易语言程序结构.txt" "Green"
}

function Generate-ReconstructedCode {
    Write-ColorText "生成重构的易语言代码..." "Yellow"
    
    # 基于分析结果生成重构代码
    $reconstructedCode = @"
.版本 2
.支持库 krnln

.程序集 窗口程序集1
.程序集变量 程序版本, 整数型, , , 393221
.程序集变量 程序类型, 整数型, , , 1
.程序集变量 语言设置, 整数型, , , 1

.程序集 FlySky设备管理
.程序集变量 设备连接状态, 逻辑型
.程序集变量 设备通信句柄, 整数型
.程序集变量 注册表主键, 文本型, , , "SOFTWARE\FlySky\E"

.子程序 _启动窗口_创建完毕
.局部变量 返回值, 逻辑型

' FlySky IDE 初始化
返回值 = 初始化IDE环境()
如果 (返回值 = 假)
    信息框("初始化失败！", 0, "FlySky易语言IDE")
    结束()
如果结束

' 加载用户配置
加载界面配置()
加载工具栏配置()
加载最近文件列表()

' 初始化设备管理
初始化FlySky设备管理器()

.子程序 初始化IDE环境, 逻辑型
.局部变量 库路径, 文本型
.局部变量 错误信息, 文本型

' 检查易语言核心库
库路径 = 取运行目录() + "\krnln.fne"
如果 (文件是否存在(库路径) = 假)
    错误信息 = "Not found the kernel library or the kernel library is invalid!"
    信息框(错误信息, 16, "错误")
    返回 (假)
如果结束

返回 (真)

.子程序 加载界面配置
.局部变量 注册表键, 整数型
.局部变量 配置数据, 字节集

' 打开注册表键
注册表键 = RegOpenKeyExA(#HKEY_CURRENT_USER, 注册表主键 + "\EInf40", 0, #KEY_READ, 0)
如果 (注册表键 ≠ 0)
    ' 读取界面配置数据
    配置数据 = 读取注册表二进制值(注册表键, "SaveE40")
    解析界面配置(配置数据)
    RegCloseKey(注册表键)
如果结束

.子程序 加载工具栏配置
.局部变量 i, 整数型
.局部变量 工具栏名, 文本型
.局部变量 注册表键, 整数型

计次循环首(14, i)
    工具栏名 = "ETBP40-Bar" + 到文本(i)
    注册表键 = RegOpenKeyExA(#HKEY_CURRENT_USER, 注册表主键 + "\" + 工具栏名, 0, #KEY_READ, 0)
    如果 (注册表键 ≠ 0)
        ' 加载工具栏配置
        RegCloseKey(注册表键)
    如果结束
计次循环尾()

.子程序 初始化FlySky设备管理器
.局部变量 初始化结果, 逻辑型

' 初始化设备通信
初始化结果 = 初始化设备通信()
如果 (初始化结果 = 真)
    设备连接状态 = 真
    更新设备状态显示()
如果结束

.子程序 初始化设备通信, 逻辑型
.局部变量 通信句柄, 整数型

' 创建设备通信套接字
通信句柄 = GetNewSock(1, 0, "", 0)
如果 (通信句柄 ≠ 0)
    设备通信句柄 = 通信句柄
    返回 (真)
如果结束

返回 (假)

.子程序 _主窗口_将被销毁
' 保存用户配置
保存界面配置()
保存工具栏配置()

' 清理资源
如果 (设备通信句柄 ≠ 0)
    关闭设备连接(设备通信句柄)
如果结束
"@

    [System.IO.File]::WriteAllText("重构的易语言代码.e", $reconstructedCode, [System.Text.Encoding]::UTF8)
    Write-ColorText "重构代码已保存到: 重构的易语言代码.e" "Green"
}

# 主程序
function Main {
    Write-ColorText "易语言代码提取工具" "Cyan"
    Write-ColorText "专门用于从Main.exe提取易语言源代码" "Gray"
    Write-ColorText "" "White"
    
    $exePath = "Main.exe"
    if (!(Test-Path $exePath)) {
        Write-ColorText "错误：当前目录下未找到Main.exe文件" "Red"
        Read-Host "按任意键退出"
        return
    }
    
    Write-ColorText "开始分析 $exePath..." "Cyan"
    
    $success = Extract-ELanguageCode $exePath
    
    if ($success) {
        Write-ColorText "`n✅ 代码提取完成！" "Green"
        Write-ColorText "生成的文件：" "White"
        Write-ColorText "  - 易语言字节码分析.txt" "Gray"
        Write-ColorText "  - 易语言字符串常量.txt" "Gray"
        Write-ColorText "  - 易语言程序结构.txt" "Gray"
        Write-ColorText "  - 重构的易语言代码.e" "Gray"
    }
    else {
        Write-ColorText "`n❌ 代码提取失败！" "Red"
    }
    
    Read-Host "`n按任意键退出"
}

# 运行主程序
Main
