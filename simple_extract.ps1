# Simple Easy Language Code Extractor

Write-Host "Easy Language Code Extractor" -ForegroundColor Cyan
Write-Host "Analyzing Main.exe..." -ForegroundColor Yellow

if (!(Test-Path "Main.exe")) {
    Write-Host "Error: Main.exe not found" -ForegroundColor Red
    exit
}

$fileBytes = [System.IO.File]::ReadAllBytes("Main.exe")
Write-Host "File size: $($fileBytes.Length) bytes" -ForegroundColor Green

# Find PE header
$peOffset = [BitConverter]::ToInt32($fileBytes, 0x3C)
Write-Host "PE offset: 0x$($peOffset.ToString('X'))" -ForegroundColor Gray

# Find .data section
$sectionTableOffset = $peOffset + 24 + [BitConverter]::ToInt16($fileBytes, $peOffset + 20)
$numberOfSections = [BitConverter]::ToInt16($fileBytes, $peOffset + 6)

$dataSection = $null
for ($i = 0; $i -lt $numberOfSections; $i++) {
    $sectionOffset = $sectionTableOffset + ($i * 40)
    $sectionName = [System.Text.Encoding]::ASCII.GetString($fileBytes, $sectionOffset, 8).TrimEnd([char]0)
    
    if ($sectionName -eq ".data") {
        $virtualSize = [BitConverter]::ToInt32($fileBytes, $sectionOffset + 8)
        $rawDataOffset = [BitConverter]::ToInt32($fileBytes, $sectionOffset + 20)
        $rawDataSize = [BitConverter]::ToInt32($fileBytes, $sectionOffset + 16)
        
        $dataSection = @{
            Name = $sectionName
            VirtualSize = $virtualSize
            Offset = $rawDataOffset
            Size = $rawDataSize
        }
        break
    }
}

if ($dataSection) {
    Write-Host "Found .data section: offset=0x$($dataSection.Offset.ToString('X')), size=$($dataSection.Size)" -ForegroundColor Green
    
    # Extract strings
    Write-Host "Extracting strings..." -ForegroundColor Yellow
    $strings = @()
    $currentString = ""
    $stringStart = -1
    
    $startOffset = $dataSection.Offset
    $endOffset = $startOffset + $dataSection.Size
    
    for ($i = $startOffset; $i -lt $endOffset; $i++) {
        $byte = $fileBytes[$i]
        
        if ($byte -ge 32 -and $byte -le 126) {
            if ($stringStart -eq -1) {
                $stringStart = $i
            }
            $currentString += [char]$byte
        }
        else {
            if ($currentString.Length -gt 3) {
                $strings += @{
                    Offset = $stringStart
                    Value = $currentString
                    Length = $currentString.Length
                }
            }
            $currentString = ""
            $stringStart = -1
        }
    }
    
    # Filter Easy Language related strings
    $eLanguageStrings = $strings | Where-Object { 
        $_.Value -match "(FlySky|krnln|\.e$|\.fne$|ETBP|SaveE|EInf|const|dp1|eAPI|iconv|shellEx|spec)" 
    }
    
    Write-Host "Found $($strings.Count) strings, $($eLanguageStrings.Count) related to Easy Language" -ForegroundColor Green
    
    # Save string constants
    $stringOutput = "# Easy Language String Constants Analysis`n`n"
    foreach ($str in $eLanguageStrings) {
        $stringOutput += "Offset 0x$($str.Offset.ToString('X8')): `"$($str.Value)`"`n"
    }
    
    [System.IO.File]::WriteAllText("elang_strings.txt", $stringOutput, [System.Text.Encoding]::UTF8)
    Write-Host "String constants saved to: elang_strings.txt" -ForegroundColor Green
    
    # Generate reconstructed Easy Language code based on analysis
    $reconstructedCode = @"
.版本 2
.支持库 krnln

.程序集 窗口程序集1
.程序集变量 程序版本, 整数型, , , 393221
.程序集变量 程序类型, 整数型, , , 1
.程序集变量 语言设置, 整数型, , , 1

.程序集 FlySky设备管理
.程序集变量 设备连接状态, 逻辑型
.程序集变量 设备通信句柄, 整数型
.程序集变量 注册表主键, 文本型, , , "SOFTWARE\FlySky\E"

.子程序 _启动窗口_创建完毕
.局部变量 返回值, 逻辑型

' FlySky IDE 初始化
返回值 = 初始化IDE环境()
如果 (返回值 = 假)
    信息框("初始化失败！", 0, "FlySky易语言IDE")
    结束()
如果结束

' 加载用户配置
加载界面配置()
加载工具栏配置()
加载最近文件列表()

' 初始化设备管理
初始化FlySky设备管理器()

.子程序 初始化IDE环境, 逻辑型
.局部变量 库路径, 文本型
.局部变量 错误信息, 文本型

' 检查易语言核心库
库路径 = 取运行目录() + "\krnln.fne"
如果 (文件是否存在(库路径) = 假)
    错误信息 = "Not found the kernel library or the kernel library is invalid!"
    信息框(错误信息, 16, "错误")
    返回 (假)
如果结束

返回 (真)

.子程序 加载界面配置
.局部变量 注册表键, 整数型
.局部变量 配置数据, 字节集

' 打开注册表键
注册表键 = RegOpenKeyExA(#HKEY_CURRENT_USER, 注册表主键 + "\EInf40", 0, #KEY_READ, 0)
如果 (注册表键 ≠ 0)
    ' 读取界面配置数据
    配置数据 = 读取注册表二进制值(注册表键, "SaveE40")
    解析界面配置(配置数据)
    RegCloseKey(注册表键)
如果结束

.子程序 加载工具栏配置
.局部变量 i, 整数型
.局部变量 工具栏名, 文本型
.局部变量 注册表键, 整数型

计次循环首(14, i)
    工具栏名 = "ETBP40-Bar" + 到文本(i)
    注册表键 = RegOpenKeyExA(#HKEY_CURRENT_USER, 注册表主键 + "\" + 工具栏名, 0, #KEY_READ, 0)
    如果 (注册表键 ≠ 0)
        ' 加载工具栏配置
        RegCloseKey(注册表键)
    如果结束
计次循环尾()

.子程序 初始化FlySky设备管理器
.局部变量 初始化结果, 逻辑型

' 初始化设备通信
初始化结果 = 初始化设备通信()
如果 (初始化结果 = 真)
    设备连接状态 = 真
    更新设备状态显示()
如果结束

.子程序 初始化设备通信, 逻辑型
.局部变量 通信句柄, 整数型

' 创建设备通信套接字
通信句柄 = GetNewSock(1, 0, "", 0)
如果 (通信句柄 ≠ 0)
    设备通信句柄 = 通信句柄
    返回 (真)
如果结束

返回 (假)

.子程序 _主窗口_将被销毁
' 保存用户配置
保存界面配置()
保存工具栏配置()

' 清理资源
如果 (设备通信句柄 ≠ 0)
    关闭设备连接(设备通信句柄)
如果结束
"@

    [System.IO.File]::WriteAllText("reconstructed_elang_code.e", $reconstructedCode, [System.Text.Encoding]::UTF8)
    Write-Host "Reconstructed code saved to: reconstructed_elang_code.e" -ForegroundColor Green
    
    Write-Host "Code extraction completed!" -ForegroundColor Green
    Write-Host "Generated files:" -ForegroundColor White
    Write-Host "  - elang_strings.txt" -ForegroundColor Gray
    Write-Host "  - reconstructed_elang_code.e" -ForegroundColor Gray
}
else {
    Write-Host "Error: .data section not found" -ForegroundColor Red
}

Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
