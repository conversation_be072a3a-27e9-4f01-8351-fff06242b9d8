.版本 2
.支持库 krnln

.程序集 窗口程序集1
.程序集变量 程序版本, 整数型, , , 393221
.程序集变量 程序类型, 整数型, , , 1
.程序集变量 语言设置, 整数型, , , 1

.程序集 主窗口程序集
.程序集变量 主窗口句柄, 整数型
.程序集变量 当前项目路径, 文本型
.程序集变量 最近文件列表, 文本型, , "10"

.程序集 核心库管理
.程序集变量 核心库路径, 文本型
.程序集变量 库文件句柄, 整数型

.程序集 注册表管理
.程序集变量 注册表主键, 文本型, , , "SOFTWARE\FlySky\E"

.程序集 工具栏管理
.程序集变量 工具栏数量, 整数型, , , 14
.程序集变量 工具栏配置, 字节集, , "14"

.程序集 项目管理
.程序集变量 当前项目, 文本型
.程序集变量 项目文件列表, 文本型, , "100"

.程序集 FlySky设备管理
.程序集变量 设备连接状态, 逻辑型
.程序集变量 设备通信句柄, 整数型

.程序集 网络通信
.程序集变量 网络连接状态, 逻辑型

.程序集 界面事件处理

.子程序 _启动窗口_创建完毕
.局部变量 返回值, 逻辑型

' FlySky IDE 初始化
返回值 = 初始化IDE环境()
如果 (返回值 = 假)
    信息框("初始化失败！", 0, "FlySky易语言IDE")
    结束()
如果结束

' 加载用户配置
加载界面配置()
加载工具栏配置()
加载最近文件列表()

' 初始化设备管理
初始化FlySky设备管理器()

.子程序 初始化IDE环境, 逻辑型
.局部变量 库路径, 文本型
.局部变量 错误信息, 文本型

' 检查易语言核心库
库路径 = 取运行目录() + "\krnln.fne"
如果 (文件是否存在(库路径) = 假)
    错误信息 = "Not found the kernel library or the kernel library is invalid!"
    信息框(错误信息, 16, "错误")
    返回 (假)
如果结束

' 加载核心库
库文件句柄 = LoadLibraryA(库路径)
如果 (库文件句柄 = 0)
    返回 (假)
如果结束

返回 (真)

.子程序 加载界面配置
.局部变量 注册表键, 整数型
.局部变量 配置数据, 字节集

' 打开注册表键
注册表键 = RegOpenKeyExA(#HKEY_CURRENT_USER, 注册表主键 + "\EInf40", 0, #KEY_READ, 0)
如果 (注册表键 ≠ 0)
    ' 读取界面配置数据
    配置数据 = 读取注册表二进制值(注册表键, "SaveE40")
    解析界面配置(配置数据)
    RegCloseKey(注册表键)
如果结束

.子程序 保存界面配置
.局部变量 注册表键, 整数型
.局部变量 配置数据, 字节集

' 生成界面配置数据
配置数据 = 生成界面配置数据()

' 保存到注册表
注册表键 = RegOpenKeyExA(#HKEY_CURRENT_USER, 注册表主键 + "\EInf40", 0, #KEY_WRITE, 0)
如果 (注册表键 ≠ 0)
    写入注册表二进制值(注册表键, "SaveE40", 配置数据)
    RegCloseKey(注册表键)
如果结束

.子程序 加载工具栏配置
.局部变量 i, 整数型
.局部变量 工具栏名, 文本型
.局部变量 注册表键, 整数型

计次循环首(工具栏数量, i)
    工具栏名 = "ETBP40-Bar" + 到文本(i)
    注册表键 = RegOpenKeyExA(#HKEY_CURRENT_USER, 注册表主键 + "\" + 工具栏名, 0, #KEY_READ, 0)
    如果 (注册表键 ≠ 0)
        工具栏配置[i] = 读取注册表二进制值(注册表键, "Config")
        RegCloseKey(注册表键)
    如果结束
计次循环尾()

.子程序 应用工具栏配置
.局部变量 i, 整数型

计次循环首(工具栏数量, i)
    设置工具栏状态(i, 工具栏配置[i])
计次循环尾()

.子程序 加载最近文件列表
.局部变量 注册表键, 整数型
.局部变量 文件路径, 文本型
.局部变量 i, 整数型

注册表键 = RegOpenKeyExA(#HKEY_CURRENT_USER, 注册表主键 + "\Recent File List", 0, #KEY_READ, 0)
如果 (注册表键 ≠ 0)
    计次循环首(10, i)
        文件路径 = 读取注册表文本值(注册表键, "File" + 到文本(i))
        如果 (文件路径 ≠ "")
            最近文件列表[i] = 文件路径
        如果结束
    计次循环尾()
    RegCloseKey(注册表键)
如果结束

.子程序 打开项目文件, , , 文件路径, 文本型
.局部变量 文件内容, 文本型

如果 (文件是否存在(文件路径) = 假)
    信息框("文件不存在：" + 文件路径, 16, "错误")
    返回
如果结束

' 读取项目文件
文件内容 = 读入文件(文件路径)
解析易语言源文件(文件内容)

' 更新最近文件列表
更新最近文件列表(文件路径)

' 设置窗口标题
设置窗口标题("FlySky易语言IDE - " + 取文件名(文件路径))

.子程序 初始化FlySky设备管理器
.局部变量 初始化结果, 逻辑型

' 初始化设备通信
初始化结果 = 初始化设备通信()
如果 (初始化结果 = 真)
    设备连接状态 = 真
    更新设备状态显示()
如果结束

.子程序 初始化设备通信, 逻辑型
.局部变量 通信句柄, 整数型

' 创建设备通信套接字
通信句柄 = GetNewSock(1, 0, "", 0)
如果 (通信句柄 ≠ 0)
    设备通信句柄 = 通信句柄
    返回 (真)
如果结束

返回 (假)

.子程序 检测FlySky设备, 整数型
.局部变量 设备列表, 文本型, , "10"
.局部变量 设备数量, 整数型

' 扫描USB设备
设备数量 = 扫描USB设备(设备列表)

' 扫描无线设备  
设备数量 = 设备数量 + 扫描无线设备(设备列表)

返回 (设备数量)

.子程序 下载固件文件, 逻辑型, , 固件URL, 文本型, 保存路径, 文本型
.局部变量 下载结果, 逻辑型

' 使用HTTP下载固件
下载结果 = HTTP下载文件(固件URL, 保存路径)
如果 (下载结果 = 真)
    ' 验证固件完整性
    如果 (验证固件文件(保存路径) = 真)
        返回 (真)
    如果结束
如果结束

返回 (假)

.子程序 上传固件到设备, 逻辑型, , 固件路径, 文本型
.局部变量 上传结果, 逻辑型

如果 (设备连接状态 = 假)
    信息框("设备未连接！", 16, "错误")
    返回 (假)
如果结束

' 上传固件到设备
上传结果 = 设备固件上传(设备通信句柄, 固件路径)
返回 (上传结果)

.子程序 _主窗口_将被销毁
' 保存用户配置
保存界面配置()
保存工具栏配置()
保存最近文件列表()

' 清理资源
如果 (设备通信句柄 ≠ 0)
    关闭设备连接(设备通信句柄)
如果结束

如果 (库文件句柄 ≠ 0)
    FreeLibrary(库文件句柄)
如果结束

.子程序 _菜单_文件_打开_被选择
.局部变量 文件路径, 文本型

文件路径 = 选择文件("易语言源文件|*.e||")
如果 (文件路径 ≠ "")
    打开项目文件(文件路径)
如果结束

.子程序 _菜单_设备_连接设备_被选择
.局部变量 设备数量, 整数型

设备数量 = 检测FlySky设备()
如果 (设备数量 > 0)
    信息框("发现 " + 到文本(设备数量) + " 个FlySky设备", 64, "设备检测")
否则
    信息框("未发现FlySky设备", 48, "设备检测")
如果结束

' ========== 辅助函数模块 ==========

.子程序 解析界面配置, , , 配置数据, 字节集
' 解析界面配置数据的具体实现
' 这里需要根据实际的配置数据格式来实现

.子程序 生成界面配置数据, 字节集
' 生成界面配置数据的具体实现
' 返回当前界面配置的二进制数据

.子程序 设置工具栏状态, , , 工具栏索引, 整数型, 配置数据, 字节集
' 根据配置数据设置指定工具栏的状态

.子程序 解析易语言源文件, , , 文件内容, 文本型
' 解析易语言源文件内容并在IDE中显示

.子程序 更新最近文件列表, , , 文件路径, 文本型
' 更新最近打开文件列表

.子程序 设置窗口标题, , , 标题, 文本型
' 设置主窗口标题

.子程序 更新设备状态显示
' 更新界面上的设备连接状态显示

.子程序 扫描USB设备, 整数型, , 设备列表, 文本型, , "10"
' 扫描USB连接的FlySky设备
' 返回发现的设备数量

.子程序 扫描无线设备, 整数型, , 设备列表, 文本型, , "10"
' 扫描无线连接的FlySky设备
' 返回发现的设备数量

.子程序 HTTP下载文件, 逻辑型, , URL, 文本型, 保存路径, 文本型
' 从指定URL下载文件到本地路径
' 返回下载是否成功

.子程序 验证固件文件, 逻辑型, , 文件路径, 文本型
' 验证固件文件的完整性和有效性
' 返回验证是否通过

.子程序 设备固件上传, 逻辑型, , 通信句柄, 整数型, 固件路径, 文本型
' 将固件文件上传到设备
' 返回上传是否成功

.子程序 关闭设备连接, , , 通信句柄, 整数型
' 关闭与设备的连接

.子程序 保存工具栏配置
' 保存当前工具栏配置到注册表

.子程序 保存最近文件列表
' 保存最近文件列表到注册表

.子程序 读取注册表二进制值, 字节集, , 注册表键, 整数型, 值名, 文本型
' 从注册表读取二进制值

.子程序 写入注册表二进制值, , , 注册表键, 整数型, 值名, 文本型, 数据, 字节集
' 向注册表写入二进制值

.子程序 读取注册表文本值, 文本型, , 注册表键, 整数型, 值名, 文本型
' 从注册表读取文本值
