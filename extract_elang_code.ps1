# Easy Language Code Extractor
# Extract Easy Language source code from Main.exe

[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "易语言代码提取工具" -ForegroundColor Cyan
Write-Host "开始分析Main.exe..." -ForegroundColor Yellow

if (!(Test-Path "Main.exe")) {
    Write-Host "错误：未找到Main.exe文件" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit
}

try {
    # Read Main.exe file
    $fileBytes = [System.IO.File]::ReadAllBytes("Main.exe")
    Write-Host "文件大小: $($fileBytes.Length) 字节" -ForegroundColor Green
    
    # Find PE header
    $peOffset = [BitConverter]::ToInt32($fileBytes, 0x3C)
    Write-Host "PE头偏移: 0x$($peOffset.ToString('X'))" -ForegroundColor Gray
    
    # Find .data section
    $sectionTableOffset = $peOffset + 24 + [BitConverter]::ToInt16($fileBytes, $peOffset + 20)
    $numberOfSections = [BitConverter]::ToInt16($fileBytes, $peOffset + 6)
    
    $dataSection = $null
    for ($i = 0; $i -lt $numberOfSections; $i++) {
        $sectionOffset = $sectionTableOffset + ($i * 40)
        $sectionName = [System.Text.Encoding]::ASCII.GetString($fileBytes, $sectionOffset, 8).TrimEnd([char]0)
        
        if ($sectionName -eq ".data") {
            $virtualSize = [BitConverter]::ToInt32($fileBytes, $sectionOffset + 8)
            $rawDataOffset = [BitConverter]::ToInt32($fileBytes, $sectionOffset + 20)
            $rawDataSize = [BitConverter]::ToInt32($fileBytes, $sectionOffset + 16)
            
            $dataSection = @{
                Name = $sectionName
                VirtualSize = $virtualSize
                Offset = $rawDataOffset
                Size = $rawDataSize
            }
            break
        }
    }
    
    if ($dataSection) {
        Write-Host "找到.data段: 偏移=0x$($dataSection.Offset.ToString('X')), 大小=$($dataSection.Size)" -ForegroundColor Green
        
        # Extract strings
        Write-Host "提取字符串常量..." -ForegroundColor Yellow
        $strings = @()
        $currentString = ""
        $stringStart = -1
        
        $startOffset = $dataSection.Offset
        $endOffset = $startOffset + $dataSection.Size
        
        for ($i = $startOffset; $i -lt $endOffset; $i++) {
            $byte = $fileBytes[$i]
            
            if ($byte -ge 32 -and $byte -le 126) {
                if ($stringStart -eq -1) {
                    $stringStart = $i
                }
                $currentString += [char]$byte
            }
            else {
                if ($currentString.Length -gt 3) {
                    $strings += @{
                        Offset = $stringStart
                        Value = $currentString
                        Length = $currentString.Length
                    }
                }
                $currentString = ""
                $stringStart = -1
            }
        }
        
        # Filter Easy Language related strings
        $eLanguageStrings = $strings | Where-Object { 
            $_.Value -match "(FlySky|krnln|\.e$|\.fne$|ETBP|SaveE|EInf)" 
        }
        
        Write-Host "发现 $($strings.Count) 个字符串，其中 $($eLanguageStrings.Count) 个与易语言相关" -ForegroundColor Green
        
        # Save string constants
        $stringOutput = "# 易语言字符串常量分析结果`n`n"
        foreach ($str in $eLanguageStrings) {
            $stringOutput += "偏移 0x$($str.Offset.ToString('X8')): `"$($str.Value)`"`n"
        }
        
        [System.IO.File]::WriteAllText("易语言字符串常量.txt", $stringOutput, [System.Text.Encoding]::UTF8)
        Write-Host "字符串常量已保存到: 易语言字符串常量.txt" -ForegroundColor Green
        
        # Analyze bytecode patterns
        Write-Host "分析字节码模式..." -ForegroundColor Yellow
        $bytecodePatterns = @()
        
        for ($i = $startOffset; $i -lt $endOffset - 8; $i += 4) {
            $value1 = [BitConverter]::ToInt32($fileBytes, $i)
            $value2 = [BitConverter]::ToInt32($fileBytes, $i + 4)
            
            if ($value1 -gt 0 -and $value1 -lt 1000 -and $value2 -gt 0 -and $value2 -lt 1000) {
                $bytecodePatterns += @{
                    Offset = $i
                    OpCode1 = $value1
                    OpCode2 = $value2
                }
            }
        }
        
        Write-Host "发现 $($bytecodePatterns.Count) 个可能的字节码模式" -ForegroundColor Green
        
        # Save bytecode analysis
        $bytecodeOutput = "# 易语言字节码分析结果`n`n"
        $maxPatterns = [Math]::Min(100, $bytecodePatterns.Count)
        for ($j = 0; $j -lt $maxPatterns; $j++) {
            $pattern = $bytecodePatterns[$j]
            $bytecodeOutput += "偏移 0x$($pattern.Offset.ToString('X8')): OpCode=$($pattern.OpCode1), Param=$($pattern.OpCode2)`n"
        }
        
        [System.IO.File]::WriteAllText("易语言字节码分析.txt", $bytecodeOutput, [System.Text.Encoding]::UTF8)
        Write-Host "字节码分析已保存到: 易语言字节码分析.txt" -ForegroundColor Green
        
        Write-Host "✅ 代码提取完成！" -ForegroundColor Green
        Write-Host "生成的文件：" -ForegroundColor White
        Write-Host "  - 易语言字符串常量.txt" -ForegroundColor Gray
        Write-Host "  - 易语言字节码分析.txt" -ForegroundColor Gray
    }
    else {
        Write-Host "错误：未找到.data段" -ForegroundColor Red
    }
}
catch {
    Write-Host "错误：$($_.Exception.Message)" -ForegroundColor Red
}

Read-Host "Press any key to exit"
